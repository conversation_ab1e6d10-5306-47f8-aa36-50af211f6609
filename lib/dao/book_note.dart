import 'package:dasso_reader/models/book_note.dart';
import 'package:dasso_reader/utils/log/common.dart';

import 'database.dart';

Future<int> insertBookNote(BookNote bookNote) async {
  if (bookNote.id != null) {
    updateBookNoteById(bookNote);
    return bookNote.id!;
  }

  List<BookNote> bookNotes =
      await selectBookNoteByCfiAndBookId(bookNote.cfi, bookNote.bookId);
  if (bookNotes.isNotEmpty) {
    bookNote.id = bookNotes.last.id;
    updateBookNoteById(bookNote);
    return bookNote.id!;
  }

  final db = await DBHelper().database;
  return db.insert('tb_notes', bookNote.toMap());
}

Future<List<BookNote>> selectBookNoteByCfiAndBookId(
    String cfi, int bookId) async {
  final db = await DBHelper().database;
  final List<Map<String, dynamic>> maps = await db.query('tb_notes',
      where: 'cfi = ? AND book_id = ?', whereArgs: [cfi, bookId]);
  return List.generate(maps.length, (i) {
    return BookNote(
      id: maps[i]['id'] as int?,
      bookId: maps[i]['book_id'] as int,
      content: maps[i]['content'] as String,
      cfi: maps[i]['cfi'] as String,
      chapter: maps[i]['chapter'] as String,
      type: maps[i]['type'] as String,
      color: maps[i]['color'] as String,
      readerNote: maps[i]['reader_note'] as String?,
      createTime: DateTime.parse(maps[i]['create_time'] as String),
      updateTime: DateTime.parse(maps[i]['update_time'] as String),
    );
  });
}

Future<List<BookNote>> selectBookNotesByBookId(int bookId) async {
  final db = await DBHelper().database;
  final List<Map<String, dynamic>> maps =
      await db.query('tb_notes', where: 'book_id = ?', whereArgs: [bookId]);
  return List.generate(maps.length, (i) {
    try {
      return BookNote(
        id: maps[i]['id'] as int?,
        bookId: maps[i]['book_id'] as int,
        content: (maps[i]['content'] as String?) ?? '',
        cfi: (maps[i]['cfi'] as String?) ?? '',
        chapter: (maps[i]['chapter'] as String?) ?? '',
        type: (maps[i]['type'] as String?) ?? '',
        color: (maps[i]['color'] as String?) ?? '',
        readerNote: maps[i]['reader_note'] as String?,
        createTime: maps[i]['create_time'] != null
            ? DateTime.parse(maps[i]['create_time'] as String)
            : null,
        updateTime: DateTime.parse((maps[i]['update_time'] as String?) ??
            DateTime.now().toIso8601String()),
      );
    } catch (e) {
      // Log the problematic record and skip it
      AnxLog.severe('Error creating BookNote from database record $i: $e');
      AnxLog.severe('Record data: ${maps[i]}');
      // Return a placeholder that will be filtered out
      return BookNote(
        id: maps[i]['id'],
        bookId: maps[i]['book_id'],
        content: '',
        cfi: '',
        chapter: '',
        type: '',
        color: '',
        updateTime: DateTime.now(),
      );
    }
  }).where((note) => note.cfi.isNotEmpty).toList(); // Filter out invalid notes
}

Future<List<BookNote>> selectBookNotesByBookIdExcludingBookmarks(
    int bookId) async {
  final db = await DBHelper().database;
  final List<Map<String, dynamic>> maps = await db.query('tb_notes',
      where: 'book_id = ? AND type != ?', whereArgs: [bookId, 'bookmark']);
  return List.generate(maps.length, (i) {
    try {
      return BookNote(
        id: maps[i]['id'] as int?,
        bookId: maps[i]['book_id'] as int,
        content: (maps[i]['content'] as String?) ?? '',
        cfi: (maps[i]['cfi'] as String?) ?? '',
        chapter: (maps[i]['chapter'] as String?) ?? '',
        type: (maps[i]['type'] as String?) ?? '',
        color: (maps[i]['color'] as String?) ?? '',
        readerNote: maps[i]['reader_note'] as String?,
        createTime: maps[i]['create_time'] != null
            ? DateTime.parse(maps[i]['create_time'] as String)
            : null,
        updateTime: DateTime.parse((maps[i]['update_time'] as String?) ??
            DateTime.now().toIso8601String()),
      );
    } catch (e) {
      // Log the problematic record and skip it
      AnxLog.severe('Error creating BookNote from database record $i: $e');
      AnxLog.severe('Record data: ${maps[i]}');
      // Return a placeholder that will be filtered out
      return BookNote(
        id: maps[i]['id'],
        bookId: maps[i]['book_id'],
        content: '',
        cfi: '',
        chapter: '',
        type: '',
        color: '',
        updateTime: DateTime.now(),
      );
    }
  }).where((note) => note.cfi.isNotEmpty).toList(); // Filter out invalid notes
}

Future<void> updateBookNoteById(BookNote bookNote) async {
  final db = await DBHelper().database;
  await db.update(
    'tb_notes',
    bookNote.toMap(),
    where: 'id = ?',
    whereArgs: [bookNote.id],
  );
}

Future<BookNote> selectBookNoteById(int id) async {
  final db = await DBHelper().database;
  final List<Map<String, dynamic>> maps =
      await db.query('tb_notes', where: 'id = ?', whereArgs: [id]);
  return BookNote(
    id: maps[0]['id'] as int?,
    bookId: maps[0]['book_id'] as int,
    content: maps[0]['content'] as String,
    cfi: maps[0]['cfi'] as String,
    chapter: maps[0]['chapter'] as String,
    type: maps[0]['type'] as String,
    color: maps[0]['color'] as String,
    readerNote: maps[0]['reader_note'] as String?,
    createTime: DateTime.parse(maps[0]['create_time'] as String),
    updateTime: DateTime.parse(maps[0]['update_time'] as String),
  );
}

Future<List<Map<String, int>>> selectAllBookIdAndNotes() async {
  final db = await DBHelper().database;
  final List<Map<String, dynamic>> maps = await db.rawQuery(
      'SELECT book_id, COUNT(id) AS number_of_notes FROM tb_notes GROUP BY book_id ORDER BY number_of_notes DESC');
  return List.generate(maps.length, (i) {
    return {
      'bookId': maps[i]['book_id'] as int,
      'numberOfNotes': maps[i]['number_of_notes'] as int,
    };
  });
}

Future<List<Map<String, int>>>
    selectAllBookIdAndNotesExcludingBookmarks() async {
  final db = await DBHelper().database;
  final List<Map<String, dynamic>> maps = await db.rawQuery(
      'SELECT book_id, COUNT(id) AS number_of_notes FROM tb_notes WHERE type != ? GROUP BY book_id ORDER BY number_of_notes DESC',
      ['bookmark']);
  return List.generate(maps.length, (i) {
    return {
      'bookId': maps[i]['book_id'] as int,
      'numberOfNotes': maps[i]['number_of_notes'] as int,
    };
  });
}

Future<Map<String, int>> selectNumberOfNotesAndBooks() async {
  final db = await DBHelper().database;
  final List<Map<String, dynamic>> maps = await db.rawQuery(
      'SELECT COUNT(id) AS number_of_notes, COUNT(DISTINCT book_id) AS number_of_books FROM tb_notes');
  return {
    'numberOfNotes': maps[0]['number_of_notes'] as int,
    'numberOfBooks': maps[0]['number_of_books'] as int,
  };
}

Future<Map<String, int>> selectNumberOfNotesAndBooksExcludingBookmarks() async {
  final db = await DBHelper().database;
  final List<Map<String, dynamic>> maps = await db.rawQuery(
      'SELECT COUNT(id) AS number_of_notes, COUNT(DISTINCT book_id) AS number_of_books FROM tb_notes WHERE type != ?',
      ['bookmark']);
  return {
    'numberOfNotes': maps[0]['number_of_notes'] as int,
    'numberOfBooks': maps[0]['number_of_books'] as int,
  };
}

Future<void> deleteBookNoteById(int id) async {
  final db = await DBHelper().database;
  await db.delete(
    'tb_notes',
    where: 'id = ?',
    whereArgs: [id],
  );
}
